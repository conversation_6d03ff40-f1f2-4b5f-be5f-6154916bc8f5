export interface ControlBoardDto {
  id: string;
  description: string;
  state: string;
  message: string;
  value: string;
  unit: string;
}

export interface CocpitDto {
  timestamp: string;
  displayName: string;
  controlBoard: ControlBoardDto[];
}

export function parseOcrResponse(response: any): CocpitDto {
  /* console.log('response');
  console.log(response);
  const document = response.analyzeResult?.documents?.[0];
  const fields = document?.fields ?? {};
  console.log(`document: ${document}`);
  console.log(`fields: ${fields}`);

  const timestamp = fields.Timestamp?.valueString ?? '';
  const displayName = fields['Display name']?.valueString ?? '';
  console.log(`timestamp: ${timestamp}`);
  console.log(`displayName: ${displayName}`);

  const controlBoardRaw = fields['Control-Board']?.valueArray ?? [];
  console.log(`controlBoardRaw: ${controlBoardRaw}`);

  const controlBoard: ControlBoardDto[] = controlBoardRaw.map((entry: any) => {
    const obj = entry.valueObject ?? {};

    return {
      id: obj.ID?.valueString ?? '',
      description: obj.Description?.valueString ?? '',
      state: obj.State?.valueString ?? '',
      message: obj.Message?.valueString ?? '',
      value: obj.Value?.valueString ?? '',
      unit: obj.Unit?.valueString ?? '',
    };
  });
  console.log(`controlBoard: ${controlBoard}`);*/

  const timestamp = response.Timestamp.valueString ?? '';
  const displayName = response['Display name'].valueString ?? '';
  console.log(timestamp);
  console.log(displayName);
  const controlBoardRaw = response['Control-Board'].valueArray ?? [];
  const controlBoard: ControlBoardDto[] = controlBoardRaw.map((entry: any) => {
    const obj = entry.valueObject ?? {};

    return {
      id: obj.ID?.valueString ?? '',
      description: obj.Description?.valueString ?? '',
      state: obj.State?.valueString ?? '',
      message: obj.Message?.valueString ?? '',
      value: obj.Value?.valueString ?? '',
      unit: obj.Unit?.valueString ?? '',
    };
  });

  console.log(controlBoard);
  return {
    timestamp,
    displayName,
    controlBoard,
  };
}
