.ocr-header__buttons {
  button {
    margin: 10px 10px 20px 10px;

    &:nth-child(1) {
      margin-left: 0;
    }
  }
}

.dataContainer {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: calc(100vh - 60px - 44px - 50px - 84px - 40px - 20px);
  min-height: 500px;

  .canvasStyle {
    display: flex;
    justify-content: center;
    max-width: 100%;
    max-height: 100%;
  }

  .pdfContainer {
    width: 40%;
    max-height: 100%;
  }

  .columnContainer {
    width: 60%;
    min-height: 100%;
    display: flex;
    flex-direction: row;

    .columnContent {
      height: 100%;
      width: 50%;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
      margin: 0 0 0 20px;
      overflow: auto;

      & h2 {
        text-align: center;
        font-size: 16px;
        margin-bottom: 30px;
      }

      & .ms-TextField {
        margin-bottom: 10px;
      }
    }
  }
}
.fileUploadButton {
  width: 50px;
  height: 50px;
  background-color: black;
  color: blue;
}
