const DocumentIntelligence = require('@azure-rest/ai-document-intelligence').default,
  { getLongRunningPoller, isUnexpected } = require('@azure-rest/ai-document-intelligence');

export async function analyzeDocumentWithCustomModel(formUrl: string) {
  const key = '889a21d290864cc193b99e6364199254';
  const endpoint = 'https://top-formrecognizer-we-dev.cognitiveservices.azure.com/';
  const modelId = 'RasControlDashboard';

  const client = DocumentIntelligence(endpoint, { key });

  const initialResponse = await client.path('/documentModels/{modelId}:analyze', modelId).post({
    contentType: 'application/json',
    body: {
      urlSource: formUrl,
    },
  });

  if (isUnexpected(initialResponse)) {
    throw new Error(JSON.stringify(initialResponse.body.error));
  }

  const poller = getLongRunningPoller(client, initialResponse);
  const analyzeResult = (await poller.pollUntilDone()).body.analyzeResult;

  const documents = analyzeResult?.documents;
  const document = documents && documents[0];
  if (!document) {
    throw new Error('No document found in analysis result.');
  }

  console.log('Document Type:', document.docType);
  console.log('Extracted Fields:', document.fields);

  return document.fields;
}
