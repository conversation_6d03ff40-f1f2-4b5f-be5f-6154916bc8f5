import { EntityDto } from '../dto/entityDto';
import { PagedFilterAndSortedRequestOCR } from '../dto/pagedFilterAndSortedRequestOCR';
import { PagedResultDto } from '../dto/pagedResultDto';
import Endpoint from '../endpoints';
import http from '../httpService';
import { OcrDto } from './dTo/ocrDto';

class OcrService {
  public async analyzeFile(ocrInput: OcrDto) {
    const result = await http.post(Endpoint.Ocr.AnalyzeFile, {
      ids: ocrInput,
      model: 'BUNKERING_NOTES_V1',
    });
    return result.data;
  }

  public async analyzeCocpitFile(ocrInput: OcrDto) {
    /* const result = await http.post(Endpoint.Ocr.AnalyzeFile, {
      ids: ocrInput,
      model: 'BUNKERING_NOTES_V1',
    });
    return result.data;*/
  }

  public async verifyFile(ocrInput: OcrDto[]) {
    const result = await http.put(Endpoint.Ocr.VerifyFile, ocrInput);
    return result.data;
  }

  public async denyFile(ocrInput: OcrDto) {
    const result = await http.put(Endpoint.Ocr.DenyFile, ocrInput);
    return result.data;
  }

  public async moveFile(ocrInput: OcrDto) {
    const result = await http.put(Endpoint.Ocr.MoveFile, ocrInput);
    return result.data;
  }

  public async changeStatus(ocrInput: OcrDto) {
    const result = await http.post(Endpoint.Ocr.ChangeStatus, ocrInput);
    return result.data;
  }

  public async getAllByStatus(pagedFilterAndSortedRequestOCR: PagedFilterAndSortedRequestOCR): Promise<OcrDto[]> {
    const result = await http.get(Endpoint.Ocr.GetAllByStatus, {
      params: pagedFilterAndSortedRequestOCR,
    });
    return result.data.result;
  }

  public async get(entityDto: EntityDto): Promise<PagedResultDto<OcrDto>> {
    const result = await http.get(Endpoint.Ocr.Get, { params: entityDto });
    return result.data.result;
  }

  public async createMany(ocrInput: Pick<OcrDto, 'fileName' | 'fileUrl'>[]) {
    const result = await http.post(Endpoint.Ocr.CreateMany, ocrInput);
    return result.data;
  }

  public async update(updateOcrInput: OcrDto) {
    const result = await http.put(Endpoint.Ocr.Update, updateOcrInput);
    return result.data.result;
  }

  public async delete(entityDto: EntityDto) {
    const result = await http.delete(Endpoint.Ocr.Delete, {
      params: entityDto,
    });
    return result.data;
  }
}

export default new OcrService();
